# StudyNotes Application - File Organization Guide

## 📁 Simple Folder Structure

This StudyNotes application is organized in a simple, easy-to-understand way:

```
studynotes/
├── 📁 css/                    # All styling files (how the app looks)
│   ├── style.css              # Main styling
│   ├── common.css             # Shared styles
│   ├── login.css              # Login page styling
│   ├── dashboard.css          # Dashboard styling
│   ├── admin.css              # Admin section styling
│   └── [other CSS files]      # Page-specific styles
│
├── 📁 js/                     # All JavaScript files (interactive features)
│   ├── common.js              # Shared functionality
│   ├── user.js                # User interactions
│   ├── quiz.js                # Quiz features
│   ├── admin.js               # Admin functionality
│   └── [other JS files]       # Page-specific scripts
│
├── 📁 pages/                  # Main application pages
│   ├── dashboard.php          # User dashboard
│   ├── modules.php            # Module management
│   ├── notes.php              # Notes management
│   ├── profile.php            # User profile
│   ├── quizzes.php            # Quiz listing
│   ├── summaries.php          # Summary listing
│   ├── generate_quiz.php      # Quiz generation
│   ├── take_quiz.php          # Quiz taking
│   ├── view_note.php          # Individual note viewing
│   └── view_summary.php       # Individual summary viewing
│
├── 📁 admin/                  # Administrator section
│   ├── login.php              # Admin login
│   ├── dashboard.php          # Admin dashboard
│   ├── ai_settings.php        # AI configuration
│   └── [other admin files]    # Admin management tools
│
├── 📁 includes/               # Shared PHP code and templates
│   ├── header.php             # Common page header
│   ├── footer.php             # Common page footer
│   ├── sidebar.php            # Navigation sidebar
│   ├── functions.php          # Utility functions
│   ├── ai_functions.php       # AI integration
│   └── [other includes]       # Shared components
│
├── 📁 api/                    # API endpoints (for AJAX requests)
│   ├── generate_quiz.php      # Quiz generation API
│   ├── generate_summary.php   # Summary generation API
│   └── test_ai.php            # AI testing endpoint
│
├── 📁 database_files/         # Database-related files
│   ├── keyweb.sql             # Main database structure
│   └── add_difficulty_column.sql # Database updates
│
├── 📁 logs/                   # Application log files
│   └── ai_debug.log           # AI system logs
│
├── 📁 docs/                   # Documentation and guides
│   ├── README.md              # This file
│   ├── STANDARDIZATION_COMPLETE.md
│   └── [other documentation]
│
├── 📄 index.php               # Main landing page
├── 📄 login.php               # Student login page
├── 📄 logout.php              # Logout functionality
├── 📄 db_connection.php       # Database connection settings
└── 📄 ai_config.php           # AI service configuration
```

## 🎯 What Each Folder Contains

### **css/** - Visual Styling
- Contains all the files that control how the application looks
- Colors, fonts, layouts, button styles, etc.
- Easy to find and modify visual elements

### **js/** - Interactive Features  
- Contains all JavaScript files that make the app interactive
- Form validation, modal windows, dynamic content, etc.
- All client-side functionality

### **pages/** - Main Application Pages
- All the main pages users interact with
- Dashboard, notes, quizzes, profiles, etc.
- The core functionality of the application

### **admin/** - Administrator Tools
- Special section for administrators only
- User management, system settings, AI configuration
- Separate from regular user features

### **includes/** - Shared Code
- Common PHP code used across multiple pages
- Headers, footers, navigation, utility functions
- Reduces code duplication

### **api/** - Background Services
- Handles AJAX requests and API calls
- Quiz generation, summary creation, AI interactions
- Behind-the-scenes functionality

### **database_files/** - Database Setup
- SQL files for setting up and updating the database
- Database structure and migration scripts
- Important for installation and updates

### **logs/** - System Logs
- Application log files for debugging
- Error tracking and system monitoring
- Helpful for troubleshooting

### **docs/** - Documentation
- User guides, technical documentation
- Setup instructions and feature explanations
- Reference materials

## 🔧 Key Configuration Files

- **db_connection.php** - Database connection settings
- **ai_config.php** - AI service configuration
- **index.php** - Main landing page (entry point)
- **login.php** - User authentication

## 🎨 Benefits of This Organization

1. **Easy to Navigate** - Clear folder names that explain their purpose
2. **Simple to Understand** - No complex nested structures
3. **Quick to Find Files** - Related files are grouped together
4. **Easy to Maintain** - Clear separation of different file types
5. **Beginner-Friendly** - Non-programmers can understand the structure
6. **Professional Appearance** - Clean, organized, presentation-ready

## 🚀 Getting Started

1. **Main Entry Point**: Start with `index.php`
2. **Styling Changes**: Look in the `css/` folder
3. **Interactive Features**: Check the `js/` folder  
4. **Page Content**: Find pages in the `pages/` folder
5. **Admin Features**: Access through the `admin/` folder
6. **Database Setup**: Use files in `database_files/` folder

This organization makes the StudyNotes application easy to understand, maintain, and present to others!
