<?php
/**
 * Simple test script to verify the reorganized file structure works
 */

echo "<h1>StudyNotes File Structure Test</h1>";

// Test database connection
echo "<h2>Testing Database Connection</h2>";
try {
    require_once('db_connection.php');
    echo "✅ Database connection file loaded successfully<br>";
    
    $test = $conn->query("SELECT 1");
    echo "✅ Database connection working<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test includes
echo "<h2>Testing Include Files</h2>";
try {
    require_once('includes/functions.php');
    echo "✅ Functions file loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Functions file failed: " . $e->getMessage() . "<br>";
}

try {
    require_once('includes/ai_functions.php');
    echo "✅ AI functions file loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ AI functions file failed: " . $e->getMessage() . "<br>";
}

// Test file existence
echo "<h2>Testing File Structure</h2>";

$files_to_check = [
    'css/style.css' => 'Main stylesheet',
    'css/common.css' => 'Common styles',
    'js/common.js' => 'Common JavaScript',
    'js/user.js' => 'User JavaScript',
    'pages/dashboard.php' => 'Dashboard page',
    'pages/notes.php' => 'Notes page',
    'admin/login.php' => 'Admin login',
    'api/generate_quiz.php' => 'Quiz API',
    'database_files/keyweb.sql' => 'Database schema',
    'logs/ai_debug.log' => 'AI debug log',
    'ai_config.php' => 'AI configuration'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description ($file) exists<br>";
    } else {
        echo "❌ $description ($file) missing<br>";
    }
}

echo "<h2>Structure Summary</h2>";
echo "<p>The StudyNotes application has been reorganized into a simple, intuitive structure:</p>";
echo "<ul>";
echo "<li><strong>css/</strong> - All styling files</li>";
echo "<li><strong>js/</strong> - All JavaScript files</li>";
echo "<li><strong>pages/</strong> - Main application pages</li>";
echo "<li><strong>admin/</strong> - Administrator tools</li>";
echo "<li><strong>includes/</strong> - Shared PHP code</li>";
echo "<li><strong>api/</strong> - API endpoints</li>";
echo "<li><strong>database_files/</strong> - Database setup files</li>";
echo "<li><strong>logs/</strong> - Application logs</li>";
echo "<li><strong>docs/</strong> - Documentation</li>";
echo "</ul>";

echo "<h2>Next Steps</h2>";
echo "<p>1. Test the main application by visiting <a href='index.php'>index.php</a></p>";
echo "<p>2. Try logging in via <a href='login.php'>login.php</a></p>";
echo "<p>3. Check admin access via <a href='admin/login.php'>admin/login.php</a></p>";
echo "<p>4. Review the <a href='README.md'>README.md</a> for complete documentation</p>";

?>
