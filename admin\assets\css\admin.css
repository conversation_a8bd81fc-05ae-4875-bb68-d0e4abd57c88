/* Admin Dashboard Styles */

/* Header Modifications */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-name {
    color: var(--light-text);
    font-weight: 500;
}

.admin-name i {
    margin-right: 5px;
}

.logout-btn {
    color: var(--light-text);
    text-decoration: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s;
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    gap: 2rem;
    min-height: 70vh;
}

.sidebar {
    width: 250px;
    background-color: var(--card-color);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 1.5rem 0;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
}

.sidebar-menu a {
    padding: 0.8rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.sidebar-menu a i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
}

.sidebar-menu a:hover {
    background-color: rgba(101, 53, 15, 0.05);
    border-left-color: var(--accent-color);
}

.sidebar-menu a.active {
    background-color: rgba(101, 53, 15, 0.1);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
}

.content {
    flex: 1;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.page-header h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* Button Styling */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    background-color: var(--highlight-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.btn i {
    margin-right: 5px;
}

.btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Cards */
.card {
    background-color: var(--card-color);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

/* Tables */
.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 0.8rem 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.users-table th {
    color: var(--primary-color);
    font-weight: 600;
    background-color: rgba(101, 53, 15, 0.05);
}

.users-table tr:last-child td {
    border-bottom: none;
}

.users-table tr:hover td {
    background-color: rgba(101, 53, 15, 0.02);
}

.actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: none;
    border: none;
    cursor: pointer;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.edit-btn {
    color: var(--highlight-color);
}

.edit-btn:hover {
    background-color: rgba(232, 135, 30, 0.1);
}

.delete-btn {
    color: #e74c3c;
}

.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

.no-data {
    text-align: center;
    color: var(--secondary-color);
    padding: 2rem 0 !important;
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
}

.modal-content {
    background-color: var(--card-color);
    margin: 8% auto;
    width: 90%;
    max-width: 500px;
    border-radius: 12px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s;
    overflow: hidden;
}

.enhanced-modal {
    border-top: 5px solid var(--highlight-color);
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-30px);}
    to {opacity: 1; transform: translateY(0);}
}

.modal-header {
    padding: 1.2rem 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(101, 53, 15, 0.03);
}

.modal-header h3 {
    color: var(--primary-color);
    margin: 0;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header h3 i {
    color: var(--highlight-color);
}

.close {
    color: var(--secondary-color);
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close:hover {
    color: var(--primary-color);
    background-color: rgba(101, 53, 15, 0.1);
}

.modal-body {
    padding: 1.8rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-group label i {
    color: var(--highlight-color);
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="email"],
.form-group textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--highlight-color);
    box-shadow: 0 0 0 3px rgba(232, 135, 30, 0.1);
    outline: none;
    background-color: #fff;
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: var(--secondary-color);
    transition: color 0.2s ease;
}

.toggle-password:hover {
    color: var(--primary-color);
}

.password-strength-container {
    margin-top: 0.5rem;
    height: 4px;
    background-color: #eee;
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    width: 0;
    transition: width 0.3s, background-color 0.3s;
}

.password-strength-text {
    font-size: 0.8rem;
    margin-top: 0.3rem;
    color: var(--secondary-color);
}

.help-text {
    font-size: 0.8rem;
    color: var(--secondary-color);
    margin-top: 0.5rem;
    font-style: italic;
}

.input-validation {
    font-size: 0.8rem;
    margin-top: 0.3rem;
    height: 1rem;
}

.input-validation.error {
    color: #e74c3c;
}

.input-validation.success {
    color: #2ecc71;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

.btn {
    padding: 0.7rem 1.2rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;
}

.btn i {
    font-size: 0.9rem;
}

.btn-primary {
    background-color: var(--highlight-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: #f1f3f5;
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #e9ecef;
    color: var(--text-color);
    transform: translateY(-2px);
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.2);
}

.warning {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* Chart Styles */
.chart-toggle-btn {
    background-color: var(--card-color);
    color: var(--primary-color);
    border: 2px solid var(--highlight-color);
    padding: 0.6rem 1rem;
    border-radius: 30px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 3px 10px rgba(232, 135, 30, 0.1);
}

.chart-toggle-btn i {
    color: var(--highlight-color);
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.chart-toggle-btn:hover {
    background-color: var(--highlight-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(232, 135, 30, 0.2);
}

.chart-toggle-btn:hover i {
    color: white;
    transform: rotate(180deg);
}

.chart-toggle-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(232, 135, 30, 0.2);
}

.btn-animated {
    animation: buttonPulse 0.5s ease;
}

@keyframes buttonPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Success and Error Messages */
.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1.5rem;
    border-left: 4px solid #28a745;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1.5rem;
    border-left: 4px solid #dc3545;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        margin-bottom: 1.5rem;
        padding: 0.5rem 0;
    }

    .sidebar-menu {
        flex-direction: row;
        overflow-x: auto;
        padding: 0.5rem;
    }

    .sidebar-menu a {
        border-left: none;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        border-left-color: transparent;
        border-bottom-color: var(--primary-color);
    }
}