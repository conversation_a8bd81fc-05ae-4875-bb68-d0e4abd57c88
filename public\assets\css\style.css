/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:root {
    --primary-color: #65350F;       /* <PERSON> Brown */
    --secondary-color: #A67B5B;     /* Medium Brown */
    --accent-color: #D4A373;        /* Light Brown */
    --highlight-color: #E8871E;     /* Orange */
    --text-color: #2D2424;          /* Dark Brown Text */
    --light-text: #F5EBE0;          /* Light Text */
    --background-color: #F5EBE0;    /* Light Beige */
    --card-color: #FFFFFF;          /* White */
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    background-color: var(--primary-color);
    color: var(--light-text);
    padding: 1rem 2rem;
}

.logo h1 {
    font-size: 2rem;
    font-weight: bold;
}

.tagline {
    font-style: italic;
    margin-top: 5px;
}

/* Main Content Styles */
main {
    flex: 1;
    padding: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.welcome-section {
    text-align: center;
    margin-bottom: 2rem;
}

.welcome-section h2 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.welcome-section p {
    max-width: 800px;
    margin: 0 auto 1.5rem;
}

/* Features Section */
.features {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.feature-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 1.5rem;
    width: 250px;
    text-align: center;
    border-top: 3px solid var(--accent-color);
}

.feature-card i {
    font-size: 2rem;
    color: var(--highlight-color);
    margin-bottom: 0.8rem;
}

.feature-card h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.feature-card p {
    color: var(--secondary-color);
}

/* Access Section */
.access-section {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.access-card {
    background-color: var(--card-color);
    border-radius: 8px;
    padding: 1.5rem;
    width: 300px;
    text-align: center;
    border-bottom: 3px solid var(--highlight-color);
}

.access-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.8rem;
    color: var(--primary-color);
}

.access-card p {
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.access-card.admin {
    border-bottom-color: var(--primary-color);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.6rem 1.5rem;
    background-color: var(--highlight-color);
    color: var(--light-text);
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn i {
    margin-right: 8px;
}

.btn:hover {
    background-color: var(--primary-color);
}

/* Standardized slim buttons */
.btn-slim {
    padding: 4px 12px;
    font-size: 13px;
    height: 30px;
    line-height: 1.4;
    border-radius: 4px;
    font-weight: 500;
}

/* Action buttons (with + symbol) */
.btn-action {
    min-width: 160px;
    justify-content: center;
}

.btn-action i {
    font-size: 12px;
    margin-right: 6px;
}

/* Navigation buttons (back buttons) */
.btn-nav {
    min-width: 130px;
    justify-content: center;
}

.btn-nav i {
    font-size: 12px;
    margin-right: 6px;
}

.admin-btn {
    background-color: var(--primary-color);
}

.admin-btn:hover {
    background-color: var(--secondary-color);
}

/* Footer Styles */
footer {
    background-color: var(--primary-color);
    color: var(--light-text);
    text-align: center;
    padding: 1rem;
    margin-top: auto;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .features {
        gap: 1rem;
    }

    .feature-card {
        width: 100%;
        max-width: 300px;
    }

    .access-card {
        width: 100%;
        max-width: 300px;
    }
}